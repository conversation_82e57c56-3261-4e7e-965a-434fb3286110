import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from '@/components/ui/sonner';
import { AuthProvider } from '@/components/auth/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import WhatsAppButton from '@/components/WhatsAppButton';
import Index from '@/pages/Index';
import Services from '@/pages/Services';
import TrackService from '@/pages/TrackService';
import CustomerLogin from '@/pages/CustomerLogin';
import CustomerDashboard from '@/pages/CustomerDashboard';
import AdminLogin from '@/pages/AdminLogin';
import AdminDashboard from '@/pages/AdminDashboard';
import AdminEnquiries from '@/pages/AdminEnquiries';
import AdminServices from '@/pages/AdminServices';
import AdminStaff from '@/pages/AdminStaff';
import AdminSettings from '@/pages/AdminSettings';
import AdminRenewalReminders from '@/pages/AdminRenewalReminders';
import AdminAssignments from '@/pages/AdminAssignments';
import AdminWorkHistory from '@/pages/AdminWorkHistory';
import StaffLogin from '@/pages/StaffLogin';
import StaffDashboard from '@/pages/StaffDashboard';
import EnquiryDetail from '@/pages/EnquiryDetail';
import TestAuth from '@/pages/TestAuth';
import NotFound from '@/pages/NotFound';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-background">
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/services" element={<Services />} />
            <Route path="/track-service" element={<TrackService />} />
            <Route path="/test-auth" element={<TestAuth />} />
            
            {/* Customer Routes */}
            <Route path="/customer/login" element={<CustomerLogin />} />
            <Route path="/customer/dashboard" element={
              <ProtectedRoute requiredRole="customer">
                <CustomerDashboard />
              </ProtectedRoute>
            } />

            {/* Admin Routes */}
            <Route path="/admin/login" element={<AdminLogin />} />
            <Route path="/admin/dashboard" element={
              <ProtectedRoute requiredRole="admin">
                <AdminDashboard />
              </ProtectedRoute>
            } />
            <Route path="/admin/enquiries" element={
              <ProtectedRoute requiredRole="admin">
                <AdminEnquiries />
              </ProtectedRoute>
            } />
            <Route path="/admin/enquiries/:id" element={
              <ProtectedRoute requiredRole="admin">
                <EnquiryDetail />
              </ProtectedRoute>
            } />
            <Route path="/admin/services" element={
              <ProtectedRoute requiredRole="admin">
                <AdminServices />
              </ProtectedRoute>
            } />
            <Route path="/admin/staff" element={
              <ProtectedRoute requiredRole="admin">
                <AdminStaff />
              </ProtectedRoute>
            } />
            <Route path="/admin/settings" element={
              <ProtectedRoute requiredRole="admin">
                <AdminSettings />
              </ProtectedRoute>
            } />
            <Route path="/admin/renewals" element={
              <ProtectedRoute requiredRole="admin">
                <AdminRenewalReminders />
              </ProtectedRoute>
            } />
            <Route path="/admin/assignments" element={
              <ProtectedRoute requiredRole="admin">
                <AdminAssignments />
              </ProtectedRoute>
            } />
            <Route path="/admin/work-history" element={
              <ProtectedRoute requiredRole="admin">
                <AdminWorkHistory />
              </ProtectedRoute>
            } />

            {/* Staff Routes */}
            <Route path="/staff/login" element={<StaffLogin />} />
            <Route path="/staff/dashboard" element={
              <ProtectedRoute requiredRole="staff">
                <StaffDashboard />
              </ProtectedRoute>
            } />
            
            {/* 404 Route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
          <WhatsAppButton />
          <Toaster />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
