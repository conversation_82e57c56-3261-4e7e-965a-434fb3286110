import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/components/auth/AuthContext';
import { api } from '@/lib/api';

const TestAuth = () => {
  const { user, isAuthenticated, loading, login, logout } = useAuth();
  const [apiTest, setApiTest] = useState<any>(null);
  const [apiError, setApiError] = useState<string | null>(null);

  useEffect(() => {
    console.log('TestAuth mounted');
    console.log('Auth state:', { user, isAuthenticated, loading });
  }, [user, isAuthenticated, loading]);

  const testAPI = async () => {
    try {
      setApiError(null);
      console.log('Testing API call...');
      
      // Test a simple API call
      const response = await fetch('http://localhost:5000/api/auth/status', {
        credentials: 'include'
      });
      
      const data = await response.json();
      console.log('API test result:', data);
      setApiTest(data);
    } catch (error) {
      console.error('API test failed:', error);
      setApiError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const testDrivingLicenseSubmit = async () => {
    try {
      setApiError(null);
      console.log('Testing driving license submission...');
      
      const formData = new FormData();
      formData.append('licenseType', 'automatic');
      formData.append('email', '<EMAIL>');
      formData.append('phone', '+971501234567');
      formData.append('eyeTestStatus', 'completed');
      
      // Create dummy files for testing
      const dummyFile = new File(['dummy content'], 'test.jpg', { type: 'image/jpeg' });
      formData.append('emirates_id', dummyFile);
      formData.append('visa', dummyFile);
      formData.append('photo', dummyFile);
      
      const response = await api.submitDrivingLicenseRequest(formData);
      console.log('Driving license test result:', response);
      setApiTest(response);
    } catch (error) {
      console.error('Driving license test failed:', error);
      setApiError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold">Authentication Status:</h3>
              <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
              <p>Loading: {loading ? 'Yes' : 'No'}</p>
            </div>
            
            {user && (
              <div>
                <h3 className="font-semibold">User Info:</h3>
                <pre className="bg-gray-100 p-2 rounded text-sm">
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>
            )}
            
            <div className="flex gap-2">
              {!isAuthenticated ? (
                <Button onClick={login}>Login with Google</Button>
              ) : (
                <Button onClick={logout} variant="outline">Logout</Button>
              )}
              <Button onClick={testAPI} variant="outline">Test API Status</Button>
              {isAuthenticated && (
                <Button onClick={testDrivingLicenseSubmit} variant="outline">
                  Test Driving License Submit
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {apiTest && (
          <Card>
            <CardHeader>
              <CardTitle>API Test Result</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
                {JSON.stringify(apiTest, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {apiError && (
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">API Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-600">{apiError}</p>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Console Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              Check the browser console (F12) for detailed logs and errors.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TestAuth;
